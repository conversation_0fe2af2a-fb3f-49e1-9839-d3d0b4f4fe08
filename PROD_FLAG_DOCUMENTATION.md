# Production Data Flag (--prod) Documentation

## Overview

The `--prod` flag has been added to all trading CLI commands to separate production data from development/testing data. When the `--prod` flag is used, all data will be saved to and read from the `data.prod` directory instead of the default `data` directory.

## Purpose

- **Environment Separation**: Keep production data separate from development/testing data
- **Data Organization**: Maintain clean separation between different data environments
- **Production Safety**: Prevent accidental mixing of test and production data

## Directory Structure

### Default Mode (without --prod)
```
data/
├── *.json           # Input symbol files (spdr_sector_etfs.json, symbols.json, etc.)
├── yahoo/           # Yahoo Finance data
├── ai_analysis/     # AI analysis results
└── sector_analysis/ # Sector analysis results
```

### Production Mode (with --prod)
```
data.prod/
├── *.json           # Production input symbol files
├── yahoo/           # Production Yahoo Finance data
├── ai_analysis/     # Production AI analysis results
└── sector_analysis/ # Production sector analysis results
```

## Commands Supporting --prod Flag

### 1. yafetch
```bash
# Development data
trading-cli yafetch AAPL --days 30
trading-cli yafetch --file data/symbols.json --days 30

# Production data
trading-cli yafetch AAPL --days 30 --prod
trading-cli yafetch --file data.prod/symbols.json --days 30 --prod
```

### 2. analysis
```bash
# Development analysis
trading-cli analysis data/yahoo/AAPL_2025-03-06_2025-04-05.CSV

# Production analysis (informational flag)
trading-cli analysis data.prod/yahoo/AAPL_2025-03-06_2025-04-05.CSV --prod
```

### 3. aianalysis
```bash
# Development AI analysis
trading-cli aianalysis data/yahoo/AAPL_2025-03-06_2025-04-05.CSV

# Production AI analysis
trading-cli aianalysis data.prod/yahoo/AAPL_2025-03-06_2025-04-05.CSV --prod
```

### 4. sector popular
```bash
# Development sector analysis
trading-cli sector popular --symbols XLK,XLF --days 90
trading-cli sector popular --file data/spdr_sector_etfs.json --days 90

# Production sector analysis
trading-cli sector popular --symbols XLK,XLF --days 90 --prod
trading-cli sector popular --file data.prod/spdr_sector_etfs.json --days 90 --prod
```

## Usage Examples

### Complete Production Workflow

1. **Fetch production data**:
   ```bash
   trading-cli yafetch --file data.prod/spdr_sector_etfs.json --days 180 --prod
   ```

2. **Analyze sector performance (production)**:
   ```bash
   trading-cli sector popular --file data.prod/spdr_sector_etfs.json --prod
   ```

3. **AI analysis on production data**:
   ```bash
   trading-cli aianalysis data.prod/yahoo/XLK_2024-12-01_2025-06-01.CSV --prod
   ```

### Development vs Production Comparison

```bash
# Fetch same data for both environments
trading-cli yafetch AAPL --days 90          # Development
trading-cli yafetch AAPL --days 90 --prod   # Production

# Compare analysis results
trading-cli analysis data/yahoo/AAPL_*.CSV
trading-cli analysis data.prod/yahoo/AAPL_*.CSV --prod
```

## Visual Indicators

When using the `--prod` flag, commands will display:
- 📁 **"Using production data directory"** messages
- 📁 **"Analyzing production data"** indicators
- Clear file paths showing `data.prod/` directories

## Best Practices

### 1. Environment Separation
- Use default mode for development, testing, and experimentation
- Use `--prod` flag for live trading analysis and production workflows
- Keep production data clean and well-organized

### 2. Data Management
- Regularly backup production data directories
- Use version control for configuration files but not data directories
- Document production data collection schedules

### 3. Workflow Organization
```bash
# Development workflow
trading-cli yafetch AAPL --days 30
trading-cli analysis data/yahoo/AAPL_*.CSV
trading-cli aianalysis data/yahoo/AAPL_*.CSV

# Production workflow
trading-cli yafetch AAPL --days 30 --prod
trading-cli analysis data.prod/yahoo/AAPL_*.CSV --prod
trading-cli aianalysis data.prod/yahoo/AAPL_*.CSV --prod
```

### 4. Automation Scripts
```bash
#!/bin/bash
# Production data collection script

echo "Fetching production sector data..."
trading-cli yafetch --file spdr_sector_etfs.json --days 90 --prod

echo "Running sector analysis..."
trading-cli sector popular --prod

echo "Production data update complete!"
```

## File Organization

### Recommended Structure
```
project/
├── data/                    # Development data
│   ├── *.json              # Input symbol files
│   ├── yahoo/
│   ├── ai_analysis/
│   └── sector_analysis/
├── data.prod/              # Production data
│   ├── *.json              # Production input symbol files
│   ├── yahoo/
│   ├── ai_analysis/
│   └── sector_analysis/
├── scripts/
│   ├── dev_analysis.sh     # Development scripts
│   └── prod_analysis.sh    # Production scripts
└── documentation/
    ├── *.md                # Documentation files
    └── references/         # Reference materials
```

## Error Handling

The `--prod` flag includes the same error handling as regular mode:
- Directory creation if not exists
- Rate limiting protection
- Data validation
- Clear error messages with production context

## Migration

### Moving from Development to Production
```bash
# Copy development data to production
cp -r data/ data.prod/

# Or start fresh with production data
trading-cli yafetch --file production_symbols.json --days 365 --prod
```

### Backing Up Production Data
```bash
# Create timestamped backup
tar -czf "data_prod_backup_$(date +%Y%m%d_%H%M%S).tar.gz" data.prod/
```

## Integration with Existing Workflows

The `--prod` flag is designed to be:
- **Non-breaking**: All existing commands work exactly the same without the flag
- **Consistent**: Same flag behavior across all commands
- **Clear**: Visual indicators when production mode is active
- **Safe**: Separate directories prevent accidental data mixing

## Troubleshooting

### Common Issues

1. **Directory not found**: The CLI automatically creates production directories
2. **Permission errors**: Ensure write permissions for `data.prod/` directory
3. **Mixed data**: Use consistent `--prod` flag across related commands
4. **Path confusion**: Check file paths when switching between modes

### Verification Commands
```bash
# Check directory structure
ls -la data*/

# Verify production data
trading-cli analysis data.prod/yahoo/AAPL_*.CSV --prod

# Compare environments
diff -r data/ data.prod/
```
