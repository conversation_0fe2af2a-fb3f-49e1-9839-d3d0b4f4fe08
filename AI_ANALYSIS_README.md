# AI Price Action Analysis

This document describes the new AI-powered price action analysis feature in the Trading CLI.

## Overview

The `aianalysis` command uses OpenAI's GPT models to provide comprehensive price action analysis of stock market data. The AI agent analyzes technical indicators, chart patterns, and market structure to provide actionable trading insights.

## Setup

### 1. Install Dependencies

The OpenAI package is automatically installed with the trading CLI:

```bash
pip install -r requirements.txt
```

### 2. Set OpenAI API Key

You need an OpenAI API key to use this feature:

```bash
export OPENAI_API_KEY='your-openai-api-key-here'
```

You can get an API key from: https://platform.openai.com/api-keys

## Usage

### Basic Usage

Analyze a CSV file with OHLCV data:

```bash
trading-cli aianalysis data/yahoo/AAPL_2025-03-06_2025-04-05.CSV
```

### Advanced Options

```bash
# Use a different model
trading-cli aianalysis data/yahoo/AAPL_2025-03-06_2025-04-05.CSV --model gpt-3.5-turbo

# Specify symbol name explicitly
trading-cli aianalysis data/yahoo/AAPL_2025-03-06_2025-04-05.CSV --symbol AAPL

# Don't save analysis to file
trading-cli aianalysis data/yahoo/AAPL_2025-03-06_2025-04-05.CSV --no-save
```

## Features

The AI analysis includes:

### 1. Price Action Assessment
- Current trend direction and strength
- Key support and resistance levels
- Chart patterns and formations

### 2. Technical Indicator Analysis
- Moving average relationships (5, 10, 20, 50-day)
- RSI overbought/oversold conditions
- MACD momentum and divergences
- Bollinger Band analysis

### 3. Volume Analysis
- Volume confirmation of price moves
- Unusual volume patterns
- Volume ratio analysis

### 4. Market Structure
- Higher highs/lower lows patterns
- Breakouts or breakdowns
- Consolidation patterns

### 5. Trading Recommendations
- Potential entry points (long/short)
- Stop loss levels
- Target prices
- Risk assessment

### 6. Market Outlook
- Short-term (1-5 days) outlook
- Medium-term (1-4 weeks) outlook
- Key levels to watch

## Technical Indicators Calculated

The AI agent automatically calculates and analyzes:

- **Moving Averages**: 5, 10, 20, 50-day
- **RSI**: 14-period Relative Strength Index
- **MACD**: Moving Average Convergence Divergence
- **Bollinger Bands**: 20-period with 2 standard deviations
- **Volume Analysis**: Current vs average volume
- **Volatility**: Annualized volatility percentage
- **Support/Resistance**: Recent highs and lows

## Output

### Console Output
The analysis is displayed in the terminal with:
- Symbol and timestamp
- Model used for analysis
- Comprehensive AI-generated analysis

### Saved Files
Analysis results are automatically saved to `data/ai_analysis/` as JSON files containing:
- Technical indicators
- AI analysis text
- Metadata (timestamp, model used, etc.)

## Supported Models

- `gpt-4` (default, most accurate)
- `gpt-3.5-turbo` (faster, lower cost)
- Any other OpenAI chat completion model

## Cost Considerations

- GPT-4: ~$0.03-0.06 per analysis
- GPT-3.5-turbo: ~$0.002-0.004 per analysis

Costs depend on the amount of data and complexity of analysis.

## Error Handling

The command handles various error scenarios:
- Missing OpenAI API key
- Invalid CSV files
- API rate limits
- Network connectivity issues

## Example Output

```
AI PRICE ACTION ANALYSIS - AAPL
================================================================================
Model Used: gpt-4
Analysis Time: 2025-01-XX

[AI-generated analysis would appear here with detailed insights about price action,
technical indicators, trading recommendations, and market outlook]

📁 Analysis saved to: data/ai_analysis/AAPL_20250101_120000_ai_analysis.json
```

## Integration with Existing Commands

The AI analysis works seamlessly with other CLI commands:

1. Use `yafetch` to download fresh data
2. Use `aianalysis` for AI-powered insights
3. Use `analysis` for traditional technical analysis
4. Compare results for comprehensive market understanding

## Tips for Best Results

1. **Use recent data**: AI analysis is most effective with current market data
2. **Sufficient history**: Ensure at least 50+ days of data for accurate technical indicators
3. **Multiple timeframes**: Analyze different time periods for comprehensive insights
4. **Combine with traditional analysis**: Use alongside the regular `analysis` command
5. **Regular updates**: Run analysis regularly as market conditions change

## Limitations

- Requires internet connection for OpenAI API
- Analysis quality depends on data quality and completeness
- AI recommendations should be combined with your own research
- Past performance doesn't guarantee future results
- Consider market conditions and external factors not captured in price data

## Support

For issues or questions about the AI analysis feature, please check:
1. OpenAI API key is correctly set
2. Internet connection is stable
3. CSV file format matches expected OHLCV structure
4. Sufficient OpenAI API credits are available
