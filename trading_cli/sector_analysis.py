import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import json
from pathlib import Path
import time


class SectorPerformanceAnalyzer:
    """Analyzer for ETF sector performance analysis"""
    
    def __init__(self):
        """Initialize the sector performance analyzer"""
        pass
    
    def fetch_etf_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """
        Fetch historical data for multiple ETFs
        
        Args:
            symbols: List of ETF symbols
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            
        Returns:
            Dictionary with symbol as key and DataFrame as value
        """
        etf_data = {}
        
        for i, symbol in enumerate(symbols):
            try:
                # Add delay between requests to avoid rate limiting
                if i > 0:
                    time.sleep(1)  # 1 second delay between requests

                ticker = yf.Ticker(symbol)
                hist = ticker.history(start=start_date, end=end_date)

                if not hist.empty:
                    etf_data[symbol] = hist
                    print(f"✓ Fetched data for {symbol}")
                else:
                    print(f"Warning: No data found for {symbol}")

            except Exception as e:
                if "rate limit" in str(e).lower() or "too many requests" in str(e).lower():
                    print(f"Rate limited for {symbol}, waiting 5 seconds...")
                    time.sleep(5)
                    try:
                        # Retry once after rate limit
                        ticker = yf.Ticker(symbol)
                        hist = ticker.history(start=start_date, end=end_date)
                        if not hist.empty:
                            etf_data[symbol] = hist
                            print(f"✓ Fetched data for {symbol} (retry)")
                        else:
                            print(f"Warning: No data found for {symbol} (retry)")
                    except Exception as retry_e:
                        print(f"Error fetching data for {symbol} (retry): {str(retry_e)}")
                else:
                    print(f"Error fetching data for {symbol}: {str(e)}")
                
        return etf_data
    
    def calculate_performance_metrics(self, df: pd.DataFrame) -> Dict:
        """
        Calculate performance metrics for a single ETF
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            Dictionary with performance metrics
        """
        if df.empty:
            return {}
        
        # Basic price metrics
        start_price = df['Close'].iloc[0]
        end_price = df['Close'].iloc[-1]
        total_return = (end_price / start_price - 1) * 100
        
        # Weekly returns (approximate - using 7-day periods)
        weekly_returns = []
        for i in range(7, len(df), 7):
            week_start = df['Close'].iloc[i-7]
            week_end = df['Close'].iloc[i]
            weekly_return = (week_end / week_start - 1) * 100
            weekly_returns.append(weekly_return)
        
        # Monthly returns (approximate - using 30-day periods)
        monthly_returns = []
        for i in range(30, len(df), 30):
            month_start = df['Close'].iloc[i-30]
            month_end = df['Close'].iloc[i]
            monthly_return = (month_end / month_start - 1) * 100
            monthly_returns.append(monthly_return)
        
        # Volatility
        daily_returns = df['Close'].pct_change().dropna()
        volatility = daily_returns.std() * np.sqrt(252) * 100  # Annualized
        
        # Volume metrics
        avg_volume = df['Volume'].mean()
        latest_volume = df['Volume'].iloc[-1]
        volume_ratio = latest_volume / avg_volume if avg_volume > 0 else 0
        
        # High/Low metrics
        period_high = df['High'].max()
        period_low = df['Low'].min()
        current_price = df['Close'].iloc[-1]
        distance_from_high = (current_price / period_high - 1) * 100
        distance_from_low = (current_price / period_low - 1) * 100
        
        return {
            'total_return_pct': total_return,
            'weekly_returns': weekly_returns,
            'monthly_returns': monthly_returns,
            'avg_weekly_return': np.mean(weekly_returns) if weekly_returns else 0,
            'avg_monthly_return': np.mean(monthly_returns) if monthly_returns else 0,
            'volatility_pct': volatility,
            'avg_volume': avg_volume,
            'volume_ratio': volume_ratio,
            'period_high': period_high,
            'period_low': period_low,
            'current_price': current_price,
            'distance_from_high_pct': distance_from_high,
            'distance_from_low_pct': distance_from_low,
            'start_date': df.index[0].strftime('%Y-%m-%d'),
            'end_date': df.index[-1].strftime('%Y-%m-%d'),
            'trading_days': len(df)
        }
    
    def analyze_etf_performance(self, symbols: List[str], start_date: str, end_date: str) -> Dict:
        """
        Analyze performance for multiple ETFs
        
        Args:
            symbols: List of ETF symbols
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            
        Returns:
            Dictionary with analysis results
        """
        # Fetch data for all ETFs
        etf_data = self.fetch_etf_data(symbols, start_date, end_date)
        
        if not etf_data:
            return {'error': 'No data found for any of the provided symbols'}
        
        # Calculate metrics for each ETF
        results = {}
        for symbol, df in etf_data.items():
            metrics = self.calculate_performance_metrics(df)
            if metrics:
                results[symbol] = metrics
        
        # Create summary statistics
        if results:
            summary = self.create_performance_summary(results)
            return {
                'individual_performance': results,
                'summary': summary,
                'analysis_period': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'symbols_analyzed': list(results.keys()),
                    'symbols_failed': [s for s in symbols if s not in results.keys()]
                }
            }
        else:
            return {'error': 'Failed to calculate metrics for any symbols'}
    
    def create_performance_summary(self, results: Dict) -> Dict:
        """
        Create summary statistics from individual ETF results
        
        Args:
            results: Dictionary with individual ETF metrics
            
        Returns:
            Summary statistics dictionary
        """
        if not results:
            return {}
        
        # Extract metrics for ranking
        total_returns = {symbol: data['total_return_pct'] for symbol, data in results.items()}
        weekly_returns = {symbol: data['avg_weekly_return'] for symbol, data in results.items()}
        monthly_returns = {symbol: data['avg_monthly_return'] for symbol, data in results.items()}
        volatilities = {symbol: data['volatility_pct'] for symbol, data in results.items()}
        
        # Rank by performance
        best_total_return = max(total_returns, key=total_returns.get)
        worst_total_return = min(total_returns, key=total_returns.get)
        
        best_weekly_return = max(weekly_returns, key=weekly_returns.get)
        worst_weekly_return = min(weekly_returns, key=weekly_returns.get)
        
        best_monthly_return = max(monthly_returns, key=monthly_returns.get)
        worst_monthly_return = min(monthly_returns, key=monthly_returns.get)
        
        lowest_volatility = min(volatilities, key=volatilities.get)
        highest_volatility = max(volatilities, key=volatilities.get)
        
        # Calculate averages
        avg_total_return = np.mean(list(total_returns.values()))
        avg_weekly_return = np.mean(list(weekly_returns.values()))
        avg_monthly_return = np.mean(list(monthly_returns.values()))
        avg_volatility = np.mean(list(volatilities.values()))
        
        return {
            'rankings': {
                'best_total_return': {'symbol': best_total_return, 'value': total_returns[best_total_return]},
                'worst_total_return': {'symbol': worst_total_return, 'value': total_returns[worst_total_return]},
                'best_weekly_return': {'symbol': best_weekly_return, 'value': weekly_returns[best_weekly_return]},
                'worst_weekly_return': {'symbol': worst_weekly_return, 'value': weekly_returns[worst_weekly_return]},
                'best_monthly_return': {'symbol': best_monthly_return, 'value': monthly_returns[best_monthly_return]},
                'worst_monthly_return': {'symbol': worst_monthly_return, 'value': monthly_returns[worst_monthly_return]},
                'lowest_volatility': {'symbol': lowest_volatility, 'value': volatilities[lowest_volatility]},
                'highest_volatility': {'symbol': highest_volatility, 'value': volatilities[highest_volatility]}
            },
            'averages': {
                'avg_total_return_pct': avg_total_return,
                'avg_weekly_return_pct': avg_weekly_return,
                'avg_monthly_return_pct': avg_monthly_return,
                'avg_volatility_pct': avg_volatility
            },
            'sorted_by_total_return': sorted(total_returns.items(), key=lambda x: x[1], reverse=True),
            'sorted_by_weekly_return': sorted(weekly_returns.items(), key=lambda x: x[1], reverse=True),
            'sorted_by_monthly_return': sorted(monthly_returns.items(), key=lambda x: x[1], reverse=True),
            'sorted_by_volatility': sorted(volatilities.items(), key=lambda x: x[1])  # Low to high
        }
    
    def save_analysis(self, analysis_result: Dict, output_dir: str = "data/sector_analysis") -> str:
        """
        Save sector analysis results to file
        
        Args:
            analysis_result: Analysis result dictionary
            output_dir: Directory to save analysis files
            
        Returns:
            Path to saved file
        """
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        symbols = "_".join(analysis_result.get('analysis_period', {}).get('symbols_analyzed', ['unknown']))[:50]
        filename = f"sector_performance_{symbols}_{timestamp}.json"
        filepath = output_path / filename
        
        # Save analysis
        with open(filepath, 'w') as f:
            json.dump(analysis_result, f, indent=2, default=str)
        
        return str(filepath)
    
    def format_performance_table(self, results: Dict) -> str:
        """
        Format performance results into a readable table
        
        Args:
            results: Analysis results dictionary
            
        Returns:
            Formatted string table
        """
        if 'error' in results:
            return f"Error: {results['error']}"
        
        individual = results.get('individual_performance', {})
        summary = results.get('summary', {})
        
        if not individual:
            return "No performance data available"
        
        # Create performance table
        table = "\n" + "=" * 100 + "\n"
        table += "ETF SECTOR PERFORMANCE ANALYSIS\n"
        table += "=" * 100 + "\n"
        
        # Analysis period info
        period = results.get('analysis_period', {})
        table += f"Analysis Period: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}\n"
        table += f"ETFs Analyzed: {', '.join(period.get('symbols_analyzed', []))}\n"
        if period.get('symbols_failed'):
            table += f"Failed to fetch: {', '.join(period.get('symbols_failed', []))}\n"
        table += "\n"
        
        # Individual performance table
        table += "INDIVIDUAL PERFORMANCE:\n"
        table += "-" * 100 + "\n"
        table += f"{'Symbol':<8} {'Total Return':<12} {'Avg Weekly':<12} {'Avg Monthly':<12} {'Volatility':<12} {'Current Price':<12}\n"
        table += "-" * 100 + "\n"
        
        for symbol, data in individual.items():
            table += f"{symbol:<8} {data['total_return_pct']:>10.2f}% {data['avg_weekly_return']:>10.2f}% {data['avg_monthly_return']:>11.2f}% {data['volatility_pct']:>10.2f}% ${data['current_price']:>10.2f}\n"
        
        # Rankings
        if summary and 'rankings' in summary:
            rankings = summary['rankings']
            table += "\n" + "PERFORMANCE RANKINGS:\n"
            table += "-" * 50 + "\n"
            table += f"Best Total Return:    {rankings['best_total_return']['symbol']} ({rankings['best_total_return']['value']:.2f}%)\n"
            table += f"Worst Total Return:   {rankings['worst_total_return']['symbol']} ({rankings['worst_total_return']['value']:.2f}%)\n"
            table += f"Best Weekly Avg:      {rankings['best_weekly_return']['symbol']} ({rankings['best_weekly_return']['value']:.2f}%)\n"
            table += f"Best Monthly Avg:     {rankings['best_monthly_return']['symbol']} ({rankings['best_monthly_return']['value']:.2f}%)\n"
            table += f"Lowest Volatility:    {rankings['lowest_volatility']['symbol']} ({rankings['lowest_volatility']['value']:.2f}%)\n"
            table += f"Highest Volatility:   {rankings['highest_volatility']['symbol']} ({rankings['highest_volatility']['value']:.2f}%)\n"
        
        return table
