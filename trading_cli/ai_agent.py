import os
import json
from typing import Dict, Optional
import pandas as pd
import numpy as np
from openai import OpenAI
from pathlib import Path


class PriceActionAIAgent:
    """AI Agent for price action analysis using OpenAI GPT models"""
    
    def __init__(self, model: str = "gpt-4", api_key: Optional[str] = None, init_client: bool = True):
        """
        Initialize the AI agent

        Args:
            model: OpenAI model to use (default: gpt-4)
            api_key: OpenAI API key (if not provided, will use OPENAI_API_KEY env var)
            init_client: Whether to initialize OpenAI client (default: True)
        """
        self.model = model
        self.client = None

        if init_client:
            api_key = api_key or os.getenv('OPENAI_API_KEY')
            if not api_key:
                raise ValueError("OpenAI API key not found. Please set OPENAI_API_KEY environment variable or pass api_key parameter.")
            self.client = OpenAI(api_key=api_key)
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> Dict:
        """Calculate comprehensive technical indicators for AI analysis"""
        
        # Price-based indicators
        latest_price = df['Close'].iloc[-1]
        price_change = (df['Close'].iloc[-1] / df['Close'].iloc[-2] - 1) * 100
        total_return = (df['Close'].iloc[-1] / df['Close'].iloc[0] - 1) * 100
        
        # Moving averages
        ma5 = df['Close'].rolling(window=5).mean()
        ma10 = df['Close'].rolling(window=10).mean()
        ma20 = df['Close'].rolling(window=20).mean()
        ma50 = df['Close'].rolling(window=50).mean()
        
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # MACD
        ema12 = df['Close'].ewm(span=12).mean()
        ema26 = df['Close'].ewm(span=26).mean()
        macd = ema12 - ema26
        macd_signal = macd.ewm(span=9).mean()
        macd_histogram = macd - macd_signal
        
        # Bollinger Bands
        bb_period = 20
        bb_std = 2
        bb_middle = df['Close'].rolling(window=bb_period).mean()
        bb_std_dev = df['Close'].rolling(window=bb_period).std()
        bb_upper = bb_middle + (bb_std_dev * bb_std)
        bb_lower = bb_middle - (bb_std_dev * bb_std)
        
        # Volume analysis
        avg_volume = df['Volume'].mean()
        volume_ratio = df['Volume'].iloc[-1] / avg_volume
        
        # Volatility
        returns = np.log(df['Close'] / df['Close'].shift(1))
        volatility = returns.std() * np.sqrt(252) * 100  # Annualized volatility
        
        # Support and Resistance levels (simplified)
        recent_highs = df['High'].rolling(window=20).max()
        recent_lows = df['Low'].rolling(window=20).min()
        
        return {
            'price_data': {
                'current_price': latest_price,
                'daily_change_pct': price_change,
                'total_return_pct': total_return,
                'high_52w': df['High'].max(),
                'low_52w': df['Low'].min(),
            },
            'moving_averages': {
                'ma5': ma5.iloc[-1] if len(ma5) > 0 else None,
                'ma10': ma10.iloc[-1] if len(ma10) > 0 else None,
                'ma20': ma20.iloc[-1] if len(ma20) > 0 else None,
                'ma50': ma50.iloc[-1] if len(ma50) > 0 else None,
            },
            'momentum_indicators': {
                'rsi': rsi.iloc[-1] if len(rsi) > 0 else None,
                'macd': macd.iloc[-1] if len(macd) > 0 else None,
                'macd_signal': macd_signal.iloc[-1] if len(macd_signal) > 0 else None,
                'macd_histogram': macd_histogram.iloc[-1] if len(macd_histogram) > 0 else None,
            },
            'bollinger_bands': {
                'upper': bb_upper.iloc[-1] if len(bb_upper) > 0 else None,
                'middle': bb_middle.iloc[-1] if len(bb_middle) > 0 else None,
                'lower': bb_lower.iloc[-1] if len(bb_lower) > 0 else None,
            },
            'volume_analysis': {
                'current_volume': df['Volume'].iloc[-1],
                'avg_volume': avg_volume,
                'volume_ratio': volume_ratio,
            },
            'volatility': {
                'annualized_volatility_pct': volatility,
            },
            'support_resistance': {
                'recent_high': recent_highs.iloc[-1] if len(recent_highs) > 0 else None,
                'recent_low': recent_lows.iloc[-1] if len(recent_lows) > 0 else None,
            }
        }
    
    def format_price_data_for_ai(self, df: pd.DataFrame, indicators: Dict) -> str:
        """Format price data and indicators for AI analysis"""
        
        # Get recent price action (last 10 days)
        recent_data = df.tail(10)
        
        price_summary = f"""
PRICE ACTION ANALYSIS REQUEST

CURRENT MARKET DATA:
- Symbol: {getattr(df, 'symbol', 'Unknown')}
- Current Price: ${indicators['price_data']['current_price']:.2f}
- Daily Change: {indicators['price_data']['daily_change_pct']:+.2f}%
- Total Return: {indicators['price_data']['total_return_pct']:+.2f}%
- 52W High: ${indicators['price_data']['high_52w']:.2f}
- 52W Low: ${indicators['price_data']['low_52w']:.2f}

TECHNICAL INDICATORS:
Moving Averages:
- MA5: ${indicators['moving_averages']['ma5']:.2f if indicators['moving_averages']['ma5'] else 'N/A'}
- MA10: ${indicators['moving_averages']['ma10']:.2f if indicators['moving_averages']['ma10'] else 'N/A'}
- MA20: ${indicators['moving_averages']['ma20']:.2f if indicators['moving_averages']['ma20'] else 'N/A'}
- MA50: ${indicators['moving_averages']['ma50']:.2f if indicators['moving_averages']['ma50'] else 'N/A'}

Momentum:
- RSI: {indicators['momentum_indicators']['rsi']:.2f if indicators['momentum_indicators']['rsi'] else 'N/A'}
- MACD: {indicators['momentum_indicators']['macd']:.4f if indicators['momentum_indicators']['macd'] else 'N/A'}
- MACD Signal: {indicators['momentum_indicators']['macd_signal']:.4f if indicators['momentum_indicators']['macd_signal'] else 'N/A'}

Bollinger Bands:
- Upper: ${indicators['bollinger_bands']['upper']:.2f if indicators['bollinger_bands']['upper'] else 'N/A'}
- Middle: ${indicators['bollinger_bands']['middle']:.2f if indicators['bollinger_bands']['middle'] else 'N/A'}
- Lower: ${indicators['bollinger_bands']['lower']:.2f if indicators['bollinger_bands']['lower'] else 'N/A'}

Volume & Volatility:
- Current Volume: {indicators['volume_analysis']['current_volume']:,.0f}
- Avg Volume: {indicators['volume_analysis']['avg_volume']:,.0f}
- Volume Ratio: {indicators['volume_analysis']['volume_ratio']:.2f}x
- Annualized Volatility: {indicators['volatility']['annualized_volatility_pct']:.2f}%

RECENT PRICE ACTION (Last 10 Days):
"""
        
        for i, (date, row) in enumerate(recent_data.iterrows()):
            price_summary += f"Day {i+1} ({date.strftime('%Y-%m-%d')}): O=${row['Open']:.2f} H=${row['High']:.2f} L=${row['Low']:.2f} C=${row['Close']:.2f} V={row['Volume']:,.0f}\n"
        
        return price_summary
    
    def get_ai_analysis_prompt(self) -> str:
        """Get the system prompt for AI price action analysis"""
        return """You are an expert technical analyst and trader with deep knowledge of price action analysis, chart patterns, and market psychology. 

Analyze the provided market data and technical indicators to provide a comprehensive price action analysis. Your analysis should include:

1. **Price Action Assessment**: 
   - Current trend direction and strength
   - Key support and resistance levels
   - Chart patterns or formations

2. **Technical Indicator Analysis**:
   - Moving average relationships and crossovers
   - RSI overbought/oversold conditions
   - MACD momentum and divergences
   - Bollinger Band position and squeeze/expansion

3. **Volume Analysis**:
   - Volume confirmation of price moves
   - Unusual volume patterns

4. **Market Structure**:
   - Higher highs/lower lows pattern
   - Breakouts or breakdowns
   - Consolidation patterns

5. **Trading Recommendations**:
   - Potential entry points (long/short)
   - Stop loss levels
   - Target prices
   - Risk assessment

6. **Market Outlook**:
   - Short-term (1-5 days) outlook
   - Medium-term (1-4 weeks) outlook
   - Key levels to watch

Please provide actionable insights while acknowledging the inherent risks in trading. Be specific about price levels and use professional trading terminology."""

    def analyze_price_action(self, df: pd.DataFrame, symbol: str = None) -> Dict:
        """
        Perform AI-powered price action analysis

        Args:
            df: DataFrame with OHLCV data
            symbol: Optional symbol name for context

        Returns:
            Dictionary containing AI analysis results
        """
        try:
            # Check if OpenAI client is initialized
            if not self.client:
                return {
                    'success': False,
                    'error': 'OpenAI client not initialized. Please provide API key.',
                    'symbol': symbol,
                    'timestamp': pd.Timestamp.now().isoformat()
                }

            # Add symbol to dataframe for context
            if symbol:
                df.symbol = symbol

            # Calculate technical indicators
            indicators = self.calculate_technical_indicators(df)

            # Format data for AI
            price_data_text = self.format_price_data_for_ai(df, indicators)

            # Get AI analysis
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.get_ai_analysis_prompt()},
                    {"role": "user", "content": price_data_text}
                ],
                max_tokens=2000,
                temperature=0.3  # Lower temperature for more consistent analysis
            )
            
            ai_analysis = response.choices[0].message.content
            
            return {
                'success': True,
                'symbol': symbol,
                'technical_indicators': indicators,
                'ai_analysis': ai_analysis,
                'model_used': self.model,
                'timestamp': pd.Timestamp.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'symbol': symbol,
                'timestamp': pd.Timestamp.now().isoformat()
            }
    
    def save_analysis(self, analysis_result: Dict, output_dir: str = "data/ai_analysis") -> str:
        """
        Save AI analysis results to file
        
        Args:
            analysis_result: Analysis result dictionary
            output_dir: Directory to save analysis files
            
        Returns:
            Path to saved file
        """
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate filename
        timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        symbol = analysis_result.get('symbol', 'unknown')
        filename = f"{symbol}_{timestamp}_ai_analysis.json"
        filepath = output_path / filename
        
        # Save analysis
        with open(filepath, 'w') as f:
            json.dump(analysis_result, f, indent=2, default=str)
        
        return str(filepath)
