import os
import json
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
import undetected_chromedriver as uc

class TradingViewClient:
    def __init__(self):
        """TradingView client for fetching data"""
        self.driver = None
        self.init_driver()
        self.set_cookies()

    def init_driver(self):
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        self.driver = uc.Chrome(options=options)
        self.driver.implicitly_wait(10)

    def set_cookies(self):
        """Set TradingView cookies from environment variable"""
        cookies_str = os.getenv('TRADINGVIEW_COOKIES')
        if not cookies_str:
            raise ValueError("TRADINGVIEW_COOKIES environment variable not set")

        try:
            # First navigate to the domain (required before setting cookies)
            self.driver.get('https://www.tradingview.com')
            
            # Parse and set cookies
            cookies = json.loads(cookies_str)
            for cookie in cookies:
                self.driver.add_cookie(cookie)
            
            print("Successfully set TradingView cookies")
            
            # Refresh page to apply cookies
            self.driver.refresh()
            
            # Verify login status
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-name='user-menu-button']"))
                )
                print("Successfully verified login status")
            except Exception as e:
                print(f"Failed to verify login status: {str(e)}")
                raise Exception("Cookie authentication failed")
                
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format in TRADINGVIEW_COOKIES")
        except Exception as e:
            raise Exception(f"Failed to set cookies: {str(e)}")

    def fetch_data(self, symbol):
        """Fetch data for a given symbol"""
        try:
            url = f'https://www.tradingview.com/chart/?symbol={symbol}'
            self.driver.get(url)
            # ... rest of your fetch_data implementation ...
        except Exception as e:
            raise Exception(f"Failed to fetch data: {str(e)}")

    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
