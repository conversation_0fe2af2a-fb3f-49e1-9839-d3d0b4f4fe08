import click
import yfinance as yf
from datetime import datetime, timedelta
from pathlib import Path
import json
import pandas as pd
import numpy as np
from typing import Tuple
from .ai_agent import PriceActionAIAgent
from .sector_analysis import SectorPerformanceAnalyzer

def get_data_directory(base_path: str, prod: bool = False) -> Path:
    """
    Get the appropriate data directory based on prod flag

    Args:
        base_path: Base path like 'yahoo', 'ai_analysis', etc.
        prod: If True, use data.prod directory instead of data

    Returns:
        Path object for the data directory
    """
    if prod:
        return Path(f'data.prod/{base_path}')
    else:
        return Path(f'data/{base_path}')

def calculate_volatility(data: pd.DataFrame) -> float:
    """Calculate daily volatility"""
    returns = np.log(data['Close'] / data['Close'].shift(1))
    return returns.std() * np.sqrt(252)  # Annualized volatility

def calculate_moving_averages(data: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
    """Calculate 20 and 50 day moving averages"""
    ma20 = data['Close'].rolling(window=20).mean()
    ma50 = data['Close'].rolling(window=50).mean()
    return ma20, ma50

def calculate_rsi(data: pd.DataFrame, periods: int = 14) -> pd.Series:
    """Calculate Relative Strength Index"""
    delta = data['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

@click.group()
@click.version_option()
def cli():
    """Trading CLI tool for managing and analyzing trades"""
    pass

@cli.command()
@click.argument('symbol', required=False)
@click.option('--file', '-f', type=click.Path(exists=True), help='JSON file containing list of symbols')
@click.option('--start', '-s', default=None, help='Start date (YYYY-MM-DD)')
@click.option('--end', '-e', default=None, help='End date (YYYY-MM-DD)')
@click.option('--days', '-d', default=30, help='Number of days of history (default: 30)')
@click.option('--prod', is_flag=True, help='Save data to data.prod directory instead of data')
def yafetch(symbol, file, start, end, days, prod):
    """Fetch historical stock prices and volume from Yahoo Finance for given symbol(s)"""
    try:
        symbols = []
        if symbol:
            symbols.append(symbol)
        if file:
            with open(file, 'r') as f:
                file_symbols = json.load(f)
                if isinstance(file_symbols, list):
                    symbols.extend(file_symbols)
                else:
                    raise ValueError("JSON file must contain a list of symbols")

        if not symbols:
            raise click.UsageError("Please provide either a symbol or a file containing symbols")

        # If start date not provided, calculate based on days
        if not start:
            end_date = datetime.now() if not end else datetime.strptime(end, '%Y-%m-%d')
            start_date = end_date - timedelta(days=days)
            start = start_date.strftime('%Y-%m-%d')
            end = end_date.strftime('%Y-%m-%d')

        # Create data directory if it doesn't exist
        data_dir = get_data_directory('yahoo', prod)
        data_dir.mkdir(parents=True, exist_ok=True)

        if prod:
            click.echo(f"📁 Using production data directory: {data_dir}")

        for sym in symbols:
            click.echo(f"\nFetching data for {sym}...")
            
            # Fetch data from Yahoo Finance
            ticker = yf.Ticker(sym)
            hist = ticker.history(start=start, end=end)

            if hist.empty:
                click.echo(f"No data found for symbol {sym}")
                continue

            # Create filename in format: SYMBOL_FROMDATE_TODATE.csv
            filename = f"{sym}_{start}_{end}.csv"
            filename = filename.replace('/', '-').upper()
            filepath = data_dir / filename

            # Write to file
            with open(filepath, 'w') as f:
                # Write header
                f.write("Date,Open,High,Low,Close,Volume\n")
                
                # Write data rows
                for date, row in hist.iterrows():
                    f.write(f"{date.strftime('%Y-%m-%d')},"
                           f"{row['Open']:.2f},"
                           f"{row['High']:.2f},"
                           f"{row['Low']:.2f},"
                           f"{row['Close']:.2f},"
                           f"{int(row['Volume'])}\n")
            
            click.echo(f"Data saved to {filepath}")

    except Exception as e:
        click.echo(f"Error: {str(e)}")



@cli.command()
@click.argument('filepath', type=click.Path(exists=True))
@click.option('--full/--summary', default=False, help='Show full analysis or just summary')
@click.option('--prod', is_flag=True, help='Indicate analysis of production data (informational only)')
def analysis(filepath, full, prod):
    """Analyze OHLCV data from a CSV file"""
    try:
        if prod:
            click.echo("📁 Analyzing production data")

        # Read the CSV file
        df = pd.read_csv(filepath)
        df['Date'] = pd.to_datetime(df['Date'])
        df.set_index('Date', inplace=True)

        # Basic statistics
        latest_price = df['Close'].iloc[-1]
        highest_price = df['High'].max()
        lowest_price = df['Low'].min()
        avg_volume = df['Volume'].mean()
        volatility = calculate_volatility(df)
        
        # Calculate price changes
        daily_return = (df['Close'].iloc[-1] / df['Close'].iloc[-2] - 1) * 100
        total_return = (df['Close'].iloc[-1] / df['Close'].iloc[0] - 1) * 100
        
        # Moving averages
        ma20, ma50 = calculate_moving_averages(df)
        current_ma20 = ma20.iloc[-1]
        current_ma50 = ma50.iloc[-1]
        
        # RSI
        rsi = calculate_rsi(df)
        current_rsi = rsi.iloc[-1]

        # Print analysis
        click.echo("\n=== Market Analysis ===")
        click.echo("─" * 40)
        click.echo(f"Latest Price: ${latest_price:.2f}")
        click.echo(f"Daily Change: {daily_return:+.2f}%")
        click.echo(f"Total Return: {total_return:+.2f}%")
        click.echo(f"Highest Price: ${highest_price:.2f}")
        click.echo(f"Lowest Price: ${lowest_price:.2f}")
        click.echo(f"Average Volume: {int(avg_volume):,}")
        click.echo(f"Volatility (Annual): {volatility:.2f}%")
        click.echo(f"RSI (14): {current_rsi:.2f}")
        click.echo("\nMoving Averages:")
        click.echo(f"MA20: ${current_ma20:.2f}")
        click.echo(f"MA50: ${current_ma50:.2f}")

        if full:
            # Additional analysis for full report
            click.echo("\n=== Detailed Statistics ===")
            click.echo("─" * 40)
            
            # Volume analysis
            click.echo("\nVolume Analysis:")
            click.echo(f"Highest Volume: {int(df['Volume'].max()):,}")
            click.echo(f"Lowest Volume: {int(df['Volume'].min()):,}")
            
            # Price ranges
            price_range = highest_price - lowest_price
            click.echo("\nPrice Ranges:")
            click.echo(f"Total Range: ${price_range:.2f}")
            click.echo(f"Average Daily Range: ${(df['High'] - df['Low']).mean():.2f}")
            
            # Trend analysis
            above_ma20 = df['Close'].iloc[-1] > current_ma20
            above_ma50 = df['Close'].iloc[-1] > current_ma50
            click.echo("\nTrend Analysis:")
            click.echo(f"Price vs MA20: {'Above' if above_ma20 else 'Below'}")
            click.echo(f"Price vs MA50: {'Above' if above_ma50 else 'Below'}")
            
            # RSI zones
            click.echo("\nRSI Analysis:")
            if current_rsi > 70:
                click.echo("RSI indicates overbought conditions")
            elif current_rsi < 30:
                click.echo("RSI indicates oversold conditions")
            else:
                click.echo("RSI indicates neutral conditions")

    except Exception as e:
        click.echo(f"Error during analysis: {str(e)}")

@cli.command()
@click.argument('filepath', type=click.Path(exists=True))
@click.option('--model', '-m', default='gpt-4', help='OpenAI model to use (default: gpt-4)')
@click.option('--save/--no-save', default=True, help='Save AI analysis to file')
@click.option('--symbol', '-s', help='Symbol name for context (optional)')
@click.option('--prod', is_flag=True, help='Save data to data.prod directory instead of data')
def aianalysis(filepath, model, save, symbol, prod):
    """AI-powered price action analysis using OpenAI GPT models"""
    try:
        # Check for OpenAI API key
        import os
        if not os.getenv('OPENAI_API_KEY'):
            click.echo("Error: OPENAI_API_KEY environment variable not set.")
            click.echo("Please set your OpenAI API key: export OPENAI_API_KEY='your-api-key'")
            return

        # Read the CSV file
        click.echo(f"Reading data from {filepath}...")
        df = pd.read_csv(filepath)
        df['Date'] = pd.to_datetime(df['Date'])
        df.set_index('Date', inplace=True)

        # Extract symbol from filename if not provided
        if not symbol:
            filename = Path(filepath).stem
            # Try to extract symbol from filename pattern: SYMBOL_DATE_DATE
            parts = filename.split('_')
            if len(parts) >= 3:
                symbol = parts[0]
            else:
                symbol = filename

        click.echo(f"Initializing AI agent with model: {model}")
        ai_agent = PriceActionAIAgent(model=model)

        if prod:
            click.echo(f"📁 Using production data directory for AI analysis")

        click.echo(f"Analyzing price action for {symbol}...")
        click.echo("This may take a moment while the AI processes the data...")

        # Perform AI analysis
        analysis_result = ai_agent.analyze_price_action(df, symbol)

        if analysis_result['success']:
            # Display the analysis
            click.echo("\n" + "=" * 80)
            click.echo(f"AI PRICE ACTION ANALYSIS - {symbol.upper()}")
            click.echo("=" * 80)
            click.echo(f"Model Used: {analysis_result['model_used']}")
            click.echo(f"Analysis Time: {analysis_result['timestamp']}")
            click.echo("\n" + analysis_result['ai_analysis'])

            # Save analysis if requested
            if save:
                output_dir = str(get_data_directory('ai_analysis', prod))
                filepath_saved = ai_agent.save_analysis(analysis_result, output_dir)
                click.echo(f"\n📁 Analysis saved to: {filepath_saved}")
        else:
            click.echo(f"❌ Analysis failed: {analysis_result['error']}")

    except ImportError as e:
        if 'openai' in str(e):
            click.echo("Error: OpenAI package not installed.")
            click.echo("Please install it with: pip install openai>=1.0.0")
        else:
            click.echo(f"Import error: {str(e)}")
    except Exception as e:
        click.echo(f"Error during AI analysis: {str(e)}")

@cli.group()
def sector():
    """Sector and ETF analysis commands"""
    pass

@sector.command()
@click.option('--symbols', '-s', help='Comma-separated list of ETF symbols (e.g., XLK,XLF,XLV)')
@click.option('--file', '-f', type=click.Path(exists=True), help='JSON file containing list of ETF symbols')
@click.option('--start', default=None, help='Start date (YYYY-MM-DD)')
@click.option('--end', default=None, help='End date (YYYY-MM-DD)')
@click.option('--days', '-d', default=90, help='Number of days of history (default: 90)')
@click.option('--save/--no-save', default=True, help='Save analysis results to file')
@click.option('--prod', is_flag=True, help='Save data to data.prod directory instead of data')
def popular(symbols, file, start, end, days, save, prod):
    """Analyze weekly and monthly performance of SPDR Select Sector ETFs"""
    try:
        # Collect ETF symbols
        etf_symbols = []

        if symbols:
            etf_symbols.extend([s.strip().upper() for s in symbols.split(',')])

        if file:
            with open(file, 'r') as f:
                file_symbols = json.load(f)
                if isinstance(file_symbols, list):
                    etf_symbols.extend([s.upper() for s in file_symbols])
                else:
                    raise ValueError("JSON file must contain a list of symbols")

        # Use default SPDR Select Sector ETFs if none provided
        if not etf_symbols:
            etf_symbols = [
                'XLK',  # Technology Select Sector SPDR Fund
                'XLF',  # Financial Select Sector SPDR Fund
                'XLV',  # Health Care Select Sector SPDR Fund
                'XLE',  # Energy Select Sector SPDR Fund
                'XLI',  # Industrial Select Sector SPDR Fund
                'XLY',  # Consumer Discretionary Select Sector SPDR Fund
                'XLP',  # Consumer Staples Select Sector SPDR Fund
                'XLB',  # Materials Select Sector SPDR Fund
                'XLRE', # Real Estate Select Sector SPDR Fund
                'XLU',  # Utilities Select Sector SPDR Fund
                'XLC'   # Communication Services Select Sector SPDR Fund
            ]
            click.echo("No symbols provided. Using SPDR Select Sector ETFs:")
            click.echo("Analyzing all 11 major S&P 500 sectors:")
            click.echo("XLK (Technology), XLF (Financial), XLV (Health Care), XLE (Energy),")
            click.echo("XLI (Industrial), XLY (Consumer Disc.), XLP (Consumer Staples),")
            click.echo("XLB (Materials), XLRE (Real Estate), XLU (Utilities), XLC (Communication)")

        # Calculate date range
        if not start:
            end_date = datetime.now() if not end else datetime.strptime(end, '%Y-%m-%d')
            start_date = end_date - timedelta(days=days)
            start = start_date.strftime('%Y-%m-%d')
            end = end_date.strftime('%Y-%m-%d')

        click.echo(f"\nAnalyzing {len(etf_symbols)} ETFs from {start} to {end}...")
        click.echo("This may take a moment to fetch and analyze data...")

        if prod:
            click.echo(f"📁 Using production data directory for sector analysis")

        # Initialize analyzer and perform analysis
        analyzer = SectorPerformanceAnalyzer()
        results = analyzer.analyze_etf_performance(etf_symbols, start, end)

        if 'error' in results:
            click.echo(f"❌ Analysis failed: {results['error']}")
            return

        # Display results
        performance_table = analyzer.format_performance_table(results)
        click.echo(performance_table)

        # Save results if requested
        if save:
            output_dir = str(get_data_directory('sector_analysis', prod))
            filepath = analyzer.save_analysis(results, output_dir)
            click.echo(f"\n📁 Analysis saved to: {filepath}")

        # Additional insights
        summary = results.get('summary', {})
        if summary and 'averages' in summary:
            averages = summary['averages']
            click.echo(f"\n📊 MARKET INSIGHTS:")
            click.echo(f"Average Total Return: {averages['avg_total_return_pct']:+.2f}%")
            click.echo(f"Average Weekly Return: {averages['avg_weekly_return_pct']:+.2f}%")
            click.echo(f"Average Monthly Return: {averages['avg_monthly_return_pct']:+.2f}%")
            click.echo(f"Average Volatility: {averages['avg_volatility_pct']:.2f}%")

    except Exception as e:
        click.echo(f"Error during sector analysis: {str(e)}")

if __name__ == '__main__':
    cli()
