import click
import yfinance as yf
from datetime import datetime, timedelta
import os
from pathlib import Path
import json
from .tradingview import TradingViewClient
import pandas as pd
import numpy as np
from typing import Tuple

def calculate_volatility(data: pd.DataFrame) -> float:
    """Calculate daily volatility"""
    returns = np.log(data['Close'] / data['Close'].shift(1))
    return returns.std() * np.sqrt(252)  # Annualized volatility

def calculate_moving_averages(data: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
    """Calculate 20 and 50 day moving averages"""
    ma20 = data['Close'].rolling(window=20).mean()
    ma50 = data['Close'].rolling(window=50).mean()
    return ma20, ma50

def calculate_rsi(data: pd.DataFrame, periods: int = 14) -> pd.Series:
    """Calculate Relative Strength Index"""
    delta = data['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

@click.group()
@click.version_option()
def cli():
    """Trading CLI tool for managing and analyzing trades"""
    pass

@cli.command()
@click.argument('symbol', required=False)
@click.option('--file', '-f', type=click.Path(exists=True), help='JSON file containing list of symbols')
@click.option('--start', '-s', default=None, help='Start date (YYYY-MM-DD)')
@click.option('--end', '-e', default=None, help='End date (YYYY-MM-DD)')
@click.option('--days', '-d', default=30, help='Number of days of history (default: 30)')
def yafetch(symbol, file, start, end, days):
    """Fetch historical stock prices and volume from Yahoo Finance for given symbol(s)"""
    try:
        symbols = []
        if symbol:
            symbols.append(symbol)
        if file:
            with open(file, 'r') as f:
                file_symbols = json.load(f)
                if isinstance(file_symbols, list):
                    symbols.extend(file_symbols)
                else:
                    raise ValueError("JSON file must contain a list of symbols")

        if not symbols:
            raise click.UsageError("Please provide either a symbol or a file containing symbols")

        # If start date not provided, calculate based on days
        if not start:
            end_date = datetime.now() if not end else datetime.strptime(end, '%Y-%m-%d')
            start_date = end_date - timedelta(days=days)
            start = start_date.strftime('%Y-%m-%d')
            end = end_date.strftime('%Y-%m-%d')

        # Create data directory if it doesn't exist
        data_dir = Path('data/yahoo')
        data_dir.mkdir(parents=True, exist_ok=True)

        for sym in symbols:
            click.echo(f"\nFetching data for {sym}...")
            
            # Fetch data from Yahoo Finance
            ticker = yf.Ticker(sym)
            hist = ticker.history(start=start, end=end)

            if hist.empty:
                click.echo(f"No data found for symbol {sym}")
                continue

            # Create filename in format: SYMBOL_FROMDATE_TODATE.csv
            filename = f"{sym}_{start}_{end}.csv"
            filename = filename.replace('/', '-').upper()
            filepath = data_dir / filename

            # Write to file
            with open(filepath, 'w') as f:
                # Write header
                f.write("Date,Open,High,Low,Close,Volume\n")
                
                # Write data rows
                for date, row in hist.iterrows():
                    f.write(f"{date.strftime('%Y-%m-%d')},"
                           f"{row['Open']:.2f},"
                           f"{row['High']:.2f},"
                           f"{row['Low']:.2f},"
                           f"{row['Close']:.2f},"
                           f"{int(row['Volume'])}\n")
            
            click.echo(f"Data saved to {filepath}")

    except Exception as e:
        click.echo(f"Error: {str(e)}")

@cli.command()
@click.argument('symbol')
@click.option('--save/--no-save', default=True, help='Save the data to file')
@click.option('--indicators/--no-indicators', default=True, help='Fetch technical indicators')
def tvfetch(symbol, save, indicators):
    """Fetch current price and indicators from TradingView"""
    try:
        client = TradingViewClient()
        click.echo("Initializing browser...")
        client.init_driver()
        
        click.echo("Logging in to TradingView...")
        if not client.login():
            click.echo("Failed to login to TradingView")
            return
        
        click.echo(f"Fetching data for {symbol}...")
        data = client.get_symbol_data(symbol, fetch_indicators=indicators)
        
        if data:
            # Print price information
            click.echo("\nPrice Information:")
            click.echo("─" * 40)
            click.echo(f"Current Price: {data['price']}")
            
            # Print technical indicators if available
            if indicators and data.get('indicators'):
                click.echo("\nTechnical Indicators:")
                click.echo("─" * 40)
                for indicator, value in data['indicators'].items():
                    click.echo(f"{indicator}: {value}")
            
            # Save data if requested
            if save:
                filepath = client.save_data(symbol, data)
                click.echo(f"\nData saved to {filepath}")
        else:
            click.echo("Failed to fetch data")
            
    except Exception as e:
        click.echo(f"Error: {str(e)}")
    finally:
        if 'client' in locals():
            client.close()

@cli.command()
@click.argument('filepath', type=click.Path(exists=True))
@click.option('--full/--summary', default=False, help='Show full analysis or just summary')
def analysis(filepath, full):
    """Analyze OHLCV data from a CSV file"""
    try:
        # Read the CSV file
        df = pd.read_csv(filepath)
        df['Date'] = pd.to_datetime(df['Date'])
        df.set_index('Date', inplace=True)

        # Basic statistics
        latest_price = df['Close'].iloc[-1]
        highest_price = df['High'].max()
        lowest_price = df['Low'].min()
        avg_volume = df['Volume'].mean()
        volatility = calculate_volatility(df)
        
        # Calculate price changes
        daily_return = (df['Close'].iloc[-1] / df['Close'].iloc[-2] - 1) * 100
        total_return = (df['Close'].iloc[-1] / df['Close'].iloc[0] - 1) * 100
        
        # Moving averages
        ma20, ma50 = calculate_moving_averages(df)
        current_ma20 = ma20.iloc[-1]
        current_ma50 = ma50.iloc[-1]
        
        # RSI
        rsi = calculate_rsi(df)
        current_rsi = rsi.iloc[-1]

        # Print analysis
        click.echo("\n=== Market Analysis ===")
        click.echo("─" * 40)
        click.echo(f"Latest Price: ${latest_price:.2f}")
        click.echo(f"Daily Change: {daily_return:+.2f}%")
        click.echo(f"Total Return: {total_return:+.2f}%")
        click.echo(f"Highest Price: ${highest_price:.2f}")
        click.echo(f"Lowest Price: ${lowest_price:.2f}")
        click.echo(f"Average Volume: {int(avg_volume):,}")
        click.echo(f"Volatility (Annual): {volatility:.2f}%")
        click.echo(f"RSI (14): {current_rsi:.2f}")
        click.echo("\nMoving Averages:")
        click.echo(f"MA20: ${current_ma20:.2f}")
        click.echo(f"MA50: ${current_ma50:.2f}")

        if full:
            # Additional analysis for full report
            click.echo("\n=== Detailed Statistics ===")
            click.echo("─" * 40)
            
            # Volume analysis
            click.echo("\nVolume Analysis:")
            click.echo(f"Highest Volume: {int(df['Volume'].max()):,}")
            click.echo(f"Lowest Volume: {int(df['Volume'].min()):,}")
            
            # Price ranges
            price_range = highest_price - lowest_price
            click.echo("\nPrice Ranges:")
            click.echo(f"Total Range: ${price_range:.2f}")
            click.echo(f"Average Daily Range: ${(df['High'] - df['Low']).mean():.2f}")
            
            # Trend analysis
            above_ma20 = df['Close'].iloc[-1] > current_ma20
            above_ma50 = df['Close'].iloc[-1] > current_ma50
            click.echo("\nTrend Analysis:")
            click.echo(f"Price vs MA20: {'Above' if above_ma20 else 'Below'}")
            click.echo(f"Price vs MA50: {'Above' if above_ma50 else 'Below'}")
            
            # RSI zones
            click.echo("\nRSI Analysis:")
            if current_rsi > 70:
                click.echo("RSI indicates overbought conditions")
            elif current_rsi < 30:
                click.echo("RSI indicates oversold conditions")
            else:
                click.echo("RSI indicates neutral conditions")

    except Exception as e:
        click.echo(f"Error during analysis: {str(e)}")

if __name__ == '__main__':
    cli()
