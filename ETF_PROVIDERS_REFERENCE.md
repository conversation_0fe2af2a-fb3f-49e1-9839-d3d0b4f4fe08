# ETF Providers Reference Guide

This document provides a comprehensive reference for the major ETF providers' funds available in the trading CLI.

## Overview

The trading CLI now includes JSON files for the most popular ETFs from three major providers:
- **Vanguard**: Known for low-cost broad market exposure
- **iShares (BlackRock)**: Comprehensive range with innovative products
- **Fidelity**: Zero-fee funds and competitive expense ratios

## Available ETF Files

### 1. Vanguard ETFs (`vanguard_etfs.json`)
**Total ETFs**: 33 funds
**Focus**: Low-cost, broad market exposure, long-term investing

#### Key Categories:
- **Broad Market**: VTI, VOO, VXF
- **International**: VEA, VWO, VXUS
- **Value/Growth**: VTV, VUG
- **Size**: VB (Small), VO (Mid), VV (Large)
- **Dividend**: VYM, VIG
- **Bonds**: BND, VTEB, VGIT, VGLT
- **Real Estate**: VNQ, VNQI
- **Sectors**: VGT, VHT, VFH, VIS, VCR, VDC, VDE, VAW, VPU

### 2. iShares ETFs (`ishares_etfs.json`)
**Total ETFs**: 40 funds
**Focus**: Comprehensive coverage, factor investing, thematic ETFs

#### Key Categories:
- **Core Holdings**: IVV, IWM, IWF, IWD
- **International**: IEFA, IEMG, EFA, EEM
- **Fixed Income**: AGG, TLT, IEF, SHY, TIP, LQD, HYG
- **Real Estate**: IYR, REET
- **Technology**: IXN, SOXX, IYW, QTEC, IGV
- **Sectors**: IYH, IYF, IYC, IYK, IYE, IYM, IDU
- **Thematic**: ICLN, IDRV, IHAK

### 3. Fidelity ETFs (`fidelity_etfs.json`)
**Total ETFs**: 34 funds
**Focus**: Zero-fee options, sector-specific funds, competitive costs

#### Key Categories:
- **Zero-Fee**: FZROX, FZILX (Note: These are mutual funds, not ETFs)
- **Broad Market**: ITOT, SCHA, SCHB
- **International**: SCHF, SCHE, SCHC
- **Value/Growth**: SCHV, SCHG
- **Dividend**: SCHD
- **Sectors**: FTEC, FHLC, FSTA, FENY, FMAT, FIDU, FUTY, FDIS, FNCL
- **Factor**: FNDA, FNDB, FNDC, FNDE, FNDX

## Usage Examples

### Analyze Single Provider
```bash
# Vanguard ETFs performance
trading-cli sector popular --file data/vanguard_etfs.json --days 90

# iShares ETFs performance  
trading-cli sector popular --file data/ishares_etfs.json --days 90

# Fidelity ETFs performance
trading-cli sector popular --file data/fidelity_etfs.json --days 90
```

### Fetch Historical Data
```bash
# Fetch Vanguard data (development)
trading-cli yafetch --file data/vanguard_etfs.json --days 180

# Fetch iShares data (production)
trading-cli yafetch --file data.prod/ishares_etfs.json --days 180 --prod

# Fetch Fidelity data
trading-cli yafetch --file data/fidelity_etfs.json --days 90
```

### AI Analysis on Provider ETFs
```bash
# Analyze Vanguard's VTI
trading-cli aianalysis data/yahoo/VTI_*.CSV

# Analyze iShares' IVV
trading-cli aianalysis data/yahoo/IVV_*.CSV --prod

# Analyze Fidelity's ITOT
trading-cli aianalysis data/yahoo/ITOT_*.CSV
```

## Provider Comparison

### Expense Ratios (Generally)
- **Vanguard**: 0.03% - 0.25% (typically lowest)
- **iShares**: 0.03% - 0.75% (competitive, wide range)
- **Fidelity**: 0.00% - 0.45% (zero-fee options available)

### Strengths by Provider

#### Vanguard
- ✅ Lowest expense ratios
- ✅ Broad market exposure
- ✅ Long-term track record
- ✅ Investor-owned structure
- ✅ Strong dividend focus

#### iShares
- ✅ Largest ETF provider
- ✅ Most comprehensive selection
- ✅ Innovative products
- ✅ Strong international offerings
- ✅ Factor and thematic ETFs

#### Fidelity
- ✅ Zero-fee mutual funds
- ✅ Competitive expense ratios
- ✅ Strong sector ETFs
- ✅ Good customer service
- ✅ Integrated platform

## Popular ETF Comparisons

### Large Cap US Equity
- **Vanguard**: VTI (Total Stock Market), VOO (S&P 500)
- **iShares**: IVV (S&P 500), ITOT (Core S&P Total US)
- **Fidelity**: ITOT (Core S&P Total US)

### International Developed
- **Vanguard**: VEA (Developed Markets), VXUS (Total International)
- **iShares**: IEFA (Core MSCI EAFE), EFA (MSCI EAFE)
- **Fidelity**: SCHF (International Equity)

### Emerging Markets
- **Vanguard**: VWO (Emerging Markets)
- **iShares**: IEMG (Core MSCI EM), EEM (MSCI EM)
- **Fidelity**: SCHE (Emerging Markets Equity)

### Technology Sector
- **Vanguard**: VGT (Information Technology)
- **iShares**: IXN (Technology), SOXX (Semiconductors)
- **Fidelity**: FTEC (Technology)

## Makefile Integration

Add these targets to your Makefile:

```makefile
# Vanguard analysis
vanguard:
	trading-cli sector popular --file data/vanguard_etfs.json --days 90

vanguard-prod:
	trading-cli sector popular --file data.prod/vanguard_etfs.json --days 90 --prod

# iShares analysis  
ishares:
	trading-cli sector popular --file data/ishares_etfs.json --days 90

ishares-prod:
	trading-cli sector popular --file data.prod/ishares_etfs.json --days 90 --prod

# Fidelity analysis
fidelity:
	trading-cli sector popular --file data/fidelity_etfs.json --days 90

fidelity-prod:
	trading-cli sector popular --file data.prod/fidelity_etfs.json --days 90 --prod

# Compare all providers
all-providers:
	@echo "Analyzing Vanguard ETFs..."
	trading-cli sector popular --file data/vanguard_etfs.json --days 90 --no-save
	@echo "Analyzing iShares ETFs..."
	trading-cli sector popular --file data/ishares_etfs.json --days 90 --no-save
	@echo "Analyzing Fidelity ETFs..."
	trading-cli sector popular --file data/fidelity_etfs.json --days 90 --no-save
```

## Investment Strategies by Provider

### Vanguard Strategy
- **Core Holdings**: VTI + VXUS (US + International)
- **Three-Fund Portfolio**: VTI + VTIAX + BND
- **Dividend Focus**: VYM + VIGI
- **Sector Rotation**: Use Vanguard sector ETFs

### iShares Strategy
- **Core-Satellite**: IVV core + sector/thematic satellites
- **Factor Investing**: Combine value, growth, quality factors
- **Global Diversification**: ACWI for one-fund solution
- **Fixed Income Ladder**: SHY + IEF + TLT

### Fidelity Strategy
- **Zero-Cost Core**: FZROX + FZILX (mutual funds)
- **Sector Rotation**: Use Fidelity sector ETFs
- **Dividend Growth**: SCHD for dividend growth
- **Factor Tilts**: Use FNDA, FNDB series

## Risk Considerations

### Concentration Risk
- Some providers may have concentration in specific sectors
- Diversify across providers for reduced single-provider risk

### Expense Ratio Impact
- Small differences compound over time
- Consider total cost of ownership including trading costs

### Tracking Error
- Different ETFs tracking same index may have slight variations
- Monitor tracking difference over time

## Best Practices

1. **Start with Core Holdings**: Begin with broad market ETFs
2. **Add Satellites**: Use sector/thematic ETFs for specific exposure
3. **Monitor Overlap**: Avoid excessive overlap between holdings
4. **Rebalance Regularly**: Maintain target allocations
5. **Consider Tax Efficiency**: ETFs generally more tax-efficient than mutual funds
6. **Review Periodically**: Assess performance and costs annually

## Data Analysis Workflow

```bash
# 1. Fetch data for all providers
trading-cli yafetch --file data/vanguard_etfs.json --days 180
trading-cli yafetch --file data/ishares_etfs.json --days 180  
trading-cli yafetch --file data/fidelity_etfs.json --days 180

# 2. Analyze performance
trading-cli sector popular --file data/vanguard_etfs.json
trading-cli sector popular --file data/ishares_etfs.json
trading-cli sector popular --file data/fidelity_etfs.json

# 3. AI analysis on top performers
trading-cli aianalysis data/yahoo/VTI_*.CSV
trading-cli aianalysis data/yahoo/IVV_*.CSV
trading-cli aianalysis data/yahoo/ITOT_*.CSV

# 4. Compare results and make investment decisions
```
