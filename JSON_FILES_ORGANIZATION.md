# JSON Input Files Organization

## Overview

All JSON input files have been moved from the root directory to the `data/` and `data.prod/` directories for better organization and consistency with the `--prod` flag functionality.

## File Locations

### Development Environment
```
data/
├── etf_symbols.json          # Sample ETF symbols
├── spdr_sector_etfs.json     # SPDR Select Sector ETFs
├── vanguard_etfs.json        # Vanguard ETFs
├── ishares_etfs.json         # iShares ETFs
├── fidelity_etfs.json        # Fidelity ETFs
├── symbols.json              # General stock symbols
├── symbols.world.index.json  # World index symbols
├── yahoo/                    # Yahoo Finance data
├── ai_analysis/              # AI analysis results
└── sector_analysis/          # Sector analysis results
```

### Production Environment
```
data.prod/
├── etf_symbols.json          # Production ETF symbols
├── spdr_sector_etfs.json     # Production SPDR Select Sector ETFs
├── vanguard_etfs.json        # Production Vanguard ETFs
├── ishares_etfs.json         # Production iShares ETFs
├── fidelity_etfs.json        # Production Fidelity ETFs
├── symbols.json              # Production stock symbols
├── symbols.world.index.json  # Production world index symbols
├── yahoo/                    # Production Yahoo Finance data
├── ai_analysis/              # Production AI analysis results
└── sector_analysis/          # Production sector analysis results
```

## Available JSON Files

### 1. spdr_sector_etfs.json
**Content**: All 11 SPDR Select Sector ETFs
```json
[
  "XLK", "XLF", "XLV", "XLE", "XLI",
  "XLY", "XLP", "XLB", "XLRE", "XLU", "XLC"
]
```
**Usage**:
```bash
trading-cli sector popular --file data/spdr_sector_etfs.json
trading-cli yafetch --file data/spdr_sector_etfs.json --days 90
```

### 1a. spdr_etfs.json
**Content**: Comprehensive SPDR ETF collection (67 ETFs)
```json
[
  "SPY", "XLK", "XLF", "XLV", "XLE", "XLI", "XLY", "XLP", "XLB", "XLRE",
  "XLU", "XLC", "SPYG", "SPYV", "SPDW", "SPEM", "GLD", "SLV", "..."
]
```
**Usage**:
```bash
trading-cli sector popular --file data/spdr_etfs.json
make spdr-all
```

### 2. symbols.json
**Content**: General stock symbols
```json
["AAPL", "MSFT"]
```
**Usage**:
```bash
trading-cli yafetch --file data/symbols.json --days 30
```

### 3. symbols.world.index.json
**Content**: World index symbols
```json
["^GSPC", "^IXIC", "^DJI"]
```
**Usage**:
```bash
trading-cli yafetch --file data/symbols.world.index.json --days 1325
```

### 4. etf_symbols.json
**Content**: Sample ETF symbols
```json
["SPY", "QQQ", "IWM", "EFA", "VTI"]
```
**Usage**:
```bash
trading-cli sector popular --file data/etf_symbols.json
```

## Usage Examples

### Development Commands
```bash
# Fetch SPDR sector data
trading-cli yafetch --file data/spdr_sector_etfs.json --days 90

# Analyze all sectors
trading-cli sector popular --file data/spdr_sector_etfs.json

# Fetch world indices
trading-cli yafetch --file data/symbols.world.index.json --days 365

# Fetch general symbols
trading-cli yafetch --file data/symbols.json --days 30
```

### Production Commands
```bash
# Fetch SPDR sector data (production)
trading-cli yafetch --file data.prod/spdr_sector_etfs.json --days 90 --prod

# Analyze all sectors (production)
trading-cli sector popular --file data.prod/spdr_sector_etfs.json --prod

# Fetch world indices (production)
trading-cli yafetch --file data.prod/symbols.world.index.json --days 365 --prod

# Fetch general symbols (production)
trading-cli yafetch --file data.prod/symbols.json --days 30 --prod
```

## Makefile Integration

The Makefile has been updated to use the new file locations:

```makefile
# Development targets
world:
	trading-cli yafetch -f data/symbols.world.index.json -d 1325

sectors:
	trading-cli sector popular --file data/spdr_sector_etfs.json --days 90

# Production targets
world-prod:
	trading-cli yafetch -f data.prod/symbols.world.index.json -d 1325 --prod

sectors-prod:
	trading-cli sector popular --file data.prod/spdr_sector_etfs.json --days 90 --prod
```

**Usage**:
```bash
make world          # Fetch world indices (development)
make world-prod     # Fetch world indices (production)
make sectors        # Analyze sectors (development)
make sectors-prod   # Analyze sectors (production)
```

## Benefits of New Organization

### 1. **Consistency**
- All input files are in the same directory as output data
- Clear separation between development and production environments
- Consistent with `--prod` flag behavior

### 2. **Organization**
- Cleaner root directory
- Logical grouping of related files
- Easy to find and manage input files

### 3. **Environment Safety**
- Production and development files are completely separate
- No risk of accidentally using wrong environment files
- Clear visual indication of which environment you're working with

### 4. **Scalability**
- Easy to add new JSON input files
- Simple to backup entire data directories
- Clear structure for automation scripts

## File Management

### Adding New JSON Files
```bash
# Add to development
echo '["NEW1", "NEW2"]' > data/new_symbols.json

# Copy to production
cp data/new_symbols.json data.prod/

# Use in commands
trading-cli yafetch --file data/new_symbols.json --days 30
trading-cli yafetch --file data.prod/new_symbols.json --days 30 --prod
```

### Backing Up JSON Files
```bash
# Backup all input files
tar -czf json_files_backup.tar.gz data/*.json data.prod/*.json

# Restore from backup
tar -xzf json_files_backup.tar.gz
```

### Synchronizing Environments
```bash
# Copy development files to production
cp data/*.json data.prod/

# Or copy specific files
cp data/spdr_sector_etfs.json data.prod/
```

## Migration Notes

### What Changed
- ✅ All JSON files moved from root to `data/` and `data.prod/`
- ✅ Makefile updated with new paths
- ✅ Documentation updated with new examples
- ✅ Both environments have identical file sets

### What Stayed the Same
- ✅ File contents are identical
- ✅ Command syntax unchanged (just file paths updated)
- ✅ All functionality preserved
- ✅ No breaking changes to existing workflows

### Migration Commands
If you have custom JSON files in the root directory:
```bash
# Move to development
mv your_custom_file.json data/

# Copy to production
cp data/your_custom_file.json data.prod/
```

## Best Practices

1. **Keep Files Synchronized**: Ensure both `data/` and `data.prod/` have the same JSON files
2. **Use Descriptive Names**: Name JSON files clearly (e.g., `tech_stocks.json`, `dividend_etfs.json`)
3. **Validate JSON**: Ensure files contain valid JSON arrays of strings
4. **Document Custom Files**: Add comments in documentation for custom symbol lists
5. **Version Control**: Include JSON files in version control but exclude data directories

## Troubleshooting

### File Not Found Errors
```bash
# Check if file exists
ls -la data/your_file.json
ls -la data.prod/your_file.json

# Verify file content
cat data/your_file.json
```

### Invalid JSON Format
```bash
# Validate JSON syntax
python -m json.tool data/your_file.json
```

### Permission Issues
```bash
# Fix permissions
chmod 644 data/*.json data.prod/*.json
```
